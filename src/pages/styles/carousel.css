/* Main container for the carousel */
.outer-carousel-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden; /* Prevent arrow overflow on small screens */
}

/* Inner container */
.carousel-container {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* The carousel itself */
.skills-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

/* Navigation buttons wrapper */
.nav-buttons-wrapper {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
    width: 50px !important;
    height: 50px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Position the left button */
.nav-buttons-wrapper:first-of-type {
    left: -50px !important;
}

/* Position the right button */
.nav-buttons-wrapper:last-of-type {
    right: -50px !important;
}

/* Style the navigation buttons */
.carousel-nav-btn {
    background: var(--glass-hover) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: none !important;
    transition: all 0.2s ease !important;
    color: var(--icon-default) !important;
    font-size: 1.5rem !important;
}

.carousel-nav-btn:hover {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    color: var(--icon-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* Carousel slide - Compact height for content */
.carousel-slide {
    padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
    box-sizing: border-box;
    min-height: 320px; /* Compact height for content */
    height: 320px; /* Fixed height for consistency */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
    overflow: visible; /* Ensure content is fully visible */
}

/* Indicator container */
.carousel-indicators {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Removed forced first-child active state - this was causing the bug */

/* iOS-Inspired Carousel Dots - Inactive State (Only target indicator buttons) */
.carousel-indicators button:not(.carousel-nav-btn),
.carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
    min-width: 8px !important;
    width: 8px !important;
    height: 8px !important;
    padding: 6px !important;
    margin: 6px 4px !important;
    border-radius: 50% !important;
    background: rgba(142, 142, 147, 0.3) !important; /* iOS tertiary label */
    border: none !important;
    backdrop-filter: blur(20px) !important;
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
    opacity: 0.7 !important;
}

/* Hover State (Only for indicator buttons) */
.carousel-indicators button:not(.carousel-nav-btn):hover,
.carousel-indicators .MuiButton-root:not(.carousel-nav-btn):hover {
    background: rgba(142, 142, 147, 0.5) !important;
    transform: scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.16) !important;
    opacity: 0.9 !important;
}

/* iOS-Inspired Active State (Only for indicator buttons) */
.carousel-indicators button.carousel-active:not(.carousel-nav-btn),
.carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
    background: #ffffff !important; /* Pure white for iOS authenticity */
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    transform: scale(1.3) !important;
    box-shadow:
        0 3px 15px rgba(0, 0, 0, 0.25) !important,
        0 1px 6px rgba(0, 0, 0, 0.15) !important;
    opacity: 1 !important;
    backdrop-filter: blur(20px) !important;
}

/* iOS-Inspired Focus States for Accessibility Compliance */
.MuiButton-root:focus-visible {
    outline: 2px solid rgba(0, 122, 255, 0.8) !important; /* iOS blue focus ring */
    outline-offset: 2px !important;
}

/* Make sure the carousel paper has no background */
.MuiPaper-root {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Ensure the projects list container is properly sized */
.projects-list {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Large Desktop Screens (1270px+) - Maximum Content Density */
@media (min-width: 1270px) {
    .carousel-slide {
        width: 85%; /* Optimized for large screens */
        min-height: 300px; /* Compact for large screens */
        height: 300px;
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
        align-items: flex-start;
        justify-content: flex-start;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -60px !important; /* More space for large screens */
    }

    .nav-buttons-wrapper:last-of-type {
        right: -60px !important;
    }

    .carousel-nav-btn {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.6rem !important;
    }
}

/* Desktop Screens (1024px-1269px) - Balanced Layout */
@media (min-width: 1024px) and (max-width: 1269px) {
    .carousel-slide {
        width: 88%; /* Balanced width for medium-large screens */
        min-height: 310px; /* Compact height for content */
        height: 310px;
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
        align-items: flex-start;
        justify-content: flex-start;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -55px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -55px !important;
    }

    .carousel-nav-btn {
        width: 48px !important;
        height: 48px !important;
        font-size: 1.5rem !important;
    }
}

/* Tablet Screens (769px-1023px) - Optimized for Touch */
@media (min-width: 769px) and (max-width: 1023px) {
    .carousel-slide {
        width: 90%; /* Optimized for tablets */
        min-height: 340px; /* Compact height for tablets */
        height: 340px;
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
        align-items: flex-start;
        justify-content: flex-start;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -45px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -45px !important;
    }

    .carousel-nav-btn {
        width: 42px !important;
        height: 42px !important;
        font-size: 1.3rem !important;
    }

    /* Enhanced touch targets for tablet dots */
    .carousel-indicators button:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
        min-width: 12px !important;
        width: 12px !important;
        height: 12px !important;
        padding: 10px !important;
        margin: 8px 6px !important;
    }
}

/* Small Tablet/Large Mobile (481px-768px) - Balanced Approach */
@media (min-width: 481px) and (max-width: 768px) {
    .outer-carousel-container {
        width: 100%;
        padding: 0;
        margin: 0;
    }

    .projects-list {
        padding: 0;
        width: 100%;
        max-width: none;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -35px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -35px !important;
    }

    .carousel-nav-btn {
        width: 38px !important;
        height: 38px !important;
        font-size: 1.1rem !important;
        background: var(--glass-active) !important;
    }

    .carousel-slide {
        min-height: 360px; /* Compact height for small tablets */
        height: 360px;
        width: 96%; /* Wider for better mobile experience */
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
        align-items: flex-start;
        justify-content: flex-start;
    }

    /* Enhanced mobile dots for better touch interaction */
    .carousel-indicators button:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
        min-width: 11px !important;
        width: 11px !important;
        height: 11px !important;
        padding: 9px !important;
        margin: 8px 5px !important;
    }
}

/* Mobile Portrait (380px-480px) - Swipe-First Design */
@media (max-width: 480px) {
    /* Hide navigation arrows on mobile for cleaner swipe experience */
    .nav-buttons-wrapper {
        display: none !important;
    }

    .outer-carousel-container {
        width: 100vw; /* Full viewport width */
        padding: 0;
        margin: 0;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        /* Android-specific centering fixes */
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .projects-list {
        padding: 0;
        width: 100%;
        max-width: none;
        /* Enhanced Android centering */
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
        position: relative;
    }

    .carousel-slide {
        min-height: 400px; /* Increased for Android compatibility */
        height: 400px;
        width: 100%; /* Full width for mobile */
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-lg) var(--spacing-md); /* Top Right Bottom Left - extra bottom padding */
        margin: 0;
        overflow: visible;
        box-sizing: border-box;
        align-items: flex-start;
        justify-content: flex-start;
        /* Android-specific positioning */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        max-width: 100vw;
    }

    /* Enhanced carousel dots for mobile touch interaction */
    .carousel-indicators button:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
        min-width: 14px !important; /* Larger for better touch targets */
        width: 14px !important;
        height: 14px !important;
        padding: 12px !important; /* Increased touch area */
        margin: 10px 6px !important;
        /* Android touch optimization */
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
        /* Improve touch responsiveness on Android */
        cursor: pointer !important;
    }

    /* Mobile active state with enhanced visibility */
    .carousel-indicators button.carousel-active:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
        transform: scale(1.3) !important; /* Balanced scaling */
    }
}

/* Very Small Mobile (320px-380px) - Ultra-Compact Design */
@media (max-width: 380px) {
    /* Keep arrows hidden on very small screens - rely on swipe and dots */
    .nav-buttons-wrapper {
        display: none !important;
    }

    .carousel-slide {
        min-height: 420px !important; /* Increased for Android compatibility */
        height: 420px !important;
        width: 100% !important; /* Full width for very small screens */
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xl) var(--spacing-md) !important; /* Top Right Bottom Left - extra bottom padding */
        margin: 0 !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
    }

    /* Maximum touch-friendly dots for very small screens */
    .carousel-indicators button:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
        min-width: 16px !important; /* Maximum touch target */
        width: 16px !important;
        height: 16px !important;
        padding: 14px !important; /* Large touch area */
        margin: 12px 8px !important;
    }

    /* Enhanced active state for very small screens */
    .carousel-indicators button.carousel-active:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
        transform: scale(1.2) !important; /* Subtle but visible scaling */
    }

    /* Override carousel library constraints for very small screens */
    .skills-carousel,
    .skills-carousel > div,
    .skills-carousel .MuiPaper-root,
    .projects-list {
        width: 100% !important;
        max-width: none !important;
        overflow: visible !important;
    }
}

/* Enhanced Dark Mode Support for Carousel Dots */
.dark .carousel-indicators button:not(.carousel-nav-btn),
.dark .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
    background: rgba(99, 99, 102, 0.6) !important; /* More visible inactive state */
    border: 1px solid rgba(142, 142, 147, 0.4) !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.4) !important,
        0 0 0 1px rgba(255, 255, 255, 0.1) !important; /* Subtle inner glow */
}

.dark .carousel-indicators button:not(.carousel-nav-btn):hover,
.dark .carousel-indicators .MuiButton-root:not(.carousel-nav-btn):hover {
    background: rgba(142, 142, 147, 0.8) !important;
    border: 1px solid rgba(174, 174, 178, 0.6) !important;
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.5) !important,
        0 0 0 1px rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
}

.dark .carousel-indicators button.carousel-active:not(.carousel-nav-btn),
.dark .carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
    background: rgba(255, 255, 255, 0.95) !important; /* Bright white for maximum contrast */
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 4px 20px rgba(255, 255, 255, 0.25) !important,
        0 2px 10px rgba(255, 255, 255, 0.15) !important,
        0 0 0 2px rgba(255, 255, 255, 0.1) !important; /* Enhanced glow effect */
}

/* WCAG Compliance - Enhanced Contrast for Accessibility */
@media (prefers-contrast: high) {
    .carousel-indicators button:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root:not(.carousel-nav-btn) {
        background: rgba(0, 0, 0, 0.8) !important;
        border: 2px solid rgba(0, 0, 0, 0.9) !important;
    }

    .carousel-indicators button.carousel-active:not(.carousel-nav-btn),
    .carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
        background: #ffffff !important;
        border: 3px solid #000000 !important;
        transform: scale(1.5) !important;
    }

    .dark .carousel-indicators button.carousel-active:not(.carousel-nav-btn),
    .dark .carousel-indicators .MuiButton-root.carousel-active:not(.carousel-nav-btn) {
        background: #ffffff !important;
        border: 3px solid #ffffff !important;
        box-shadow: 0 0 0 2px #000000 !important;
    }
}

/* Dark mode styles */
.dark .carousel-nav-btn {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border) !important;
    color: var(--dark-icon-default) !important;
}

.dark .carousel-nav-btn:hover {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
    color: var(--dark-icon-hover) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

/* Dark mode carousel dots now use the same styling as light mode for consistency */

/* Android Browser Specific Fixes */
/* Target Android Chrome specifically */
@supports (-webkit-appearance: none) {
    @media (max-width: 768px) {
        .outer-carousel-container {
            /* Chrome on Android viewport handling */
            width: 100vw;
            max-width: 100vw;
            position: relative;
            left: 50%;
            margin-left: -50vw;
            /* Ensure proper centering */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .projects-list {
            /* Enhanced centering for Chrome on Android */
            width: 100% !important;
            max-width: 100vw !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto !important;
        }

        .carousel-slide {
            /* Chrome on Android specific positioning */
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 100vw;
            margin: 0;
        }
    }
}

/* Android Firefox specific fixes */
@-moz-document url-prefix() {
    @media (max-width: 768px) {
        .outer-carousel-container {
            /* Firefox on Android viewport handling */
            width: 100vw;
            position: relative;
            left: 50%;
            margin-left: -50vw;
            display: flex;
            justify-content: center;
        }

        .carousel-slide {
            /* Firefox on Android centering */
            margin: 0 auto;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

/* Samsung Internet and other Android browsers */
@media (max-width: 768px) and (orientation: portrait) {
    .outer-carousel-container {
        /* Android portrait mode optimization */
        width: 100vw;
        max-width: 100vw;
        overflow-x: hidden;
        position: relative;
        left: 50%;
        margin-left: -50vw;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .projects-list {
        /* Portrait mode centering */
        width: 100% !important;
        max-width: 100vw !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
        overflow: visible !important;
    }

    .carousel-slide {
        /* Portrait mode slide positioning */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: calc(100vw - 20px); /* Account for potential scrollbar */
        margin: 0;
        box-sizing: border-box;
    }
}

/* Android landscape mode fixes */
@media (max-width: 768px) and (orientation: landscape) {
    .carousel-slide {
        /* Landscape mode height adjustment for Android */
        min-height: 350px;
        height: 350px;
        /* Ensure proper centering in landscape */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        max-width: 100vw;
    }
}

/* Android-Specific Enhanced Centering (Content Bleeding Prevention Removed) */
/* Focused fixes for Android browsers to ensure perfect card centering without affecting content display */

/* Chrome on Android - Enhanced centering only */
@supports (-webkit-appearance: none) {
    @media (max-width: 768px) {
        .outer-carousel-container {
            /* Enhanced centering for Chrome on Android */
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .projects-list {
            /* Perfect centering for Chrome on Android */
            width: 100% !important;
            max-width: 100vw !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto !important;
        }

        .carousel-slide {
            /* Enhanced centering with multiple fallbacks */
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
        }
    }
}

/* Firefox on Android - Enhanced centering only */
@-moz-document url-prefix() {
    @media (max-width: 768px) {
        .outer-carousel-container {
            /* Enhanced centering for Firefox on Android */
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .projects-list {
            /* Perfect centering */
            width: 100% !important;
            max-width: 100vw !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto !important;
        }

        .carousel-slide {
            /* Enhanced centering */
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            margin: 0 auto;
        }
    }
}

/* Samsung Internet and other Android browsers - Portrait mode (centering only) */
@media (max-width: 768px) and (orientation: portrait) {
    .outer-carousel-container {
        /* Perfect centering for portrait mode */
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .projects-list {
        /* Enhanced centering with multiple techniques */
        width: 100% !important;
        max-width: 100vw !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
    }

    .carousel-slide {
        /* Perfect centering with transform */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: calc(100vw - 20px);
        margin: 0 auto;
        box-sizing: border-box;
    }
}

/* Android landscape mode - Enhanced centering only */
@media (max-width: 768px) and (orientation: landscape) {
    .outer-carousel-container {
        /* Enhanced centering for landscape */
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .projects-list {
        /* Perfect centering in landscape */
        width: 100% !important;
        max-width: 100vw !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
    }

    .carousel-slide {
        /* Enhanced centering in landscape */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        margin: 0 auto;
    }
}

/* Android very small screens - Enhanced centering only */
@media (max-width: 380px) {
    .outer-carousel-container {
        /* Perfect centering for very small screens */
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .projects-list {
        /* Enhanced centering for very small screens */
        width: 100% !important;
        max-width: 100vw !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
    }

    .carousel-slide {
        /* Perfect centering for very small screens */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: calc(100vw - 10px);
        margin: 0 auto;
        box-sizing: border-box;
    }
}


