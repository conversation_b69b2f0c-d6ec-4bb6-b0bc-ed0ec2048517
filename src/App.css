html {
    overflow-y: hidden; /* Let content-wrapper handle scrolling */
    overflow-x: hidden;
    color: #1c1c1e; /* iOS primary label color */
    background: linear-gradient(
        180deg,
        #ffffff 0%,
        #f9f9f9 8%,
        #f2f2f7 18%,
        #e5e5ea 32%,
        #d1d1d6 48%,
        #c7c7cc 65%,
        #aeaeb2 82%,
        #8e8e93 100%
    );
    background-size: 250% 250%; /* Amplified size for enhanced parallax */
    background-attachment: fixed;
    background-position: 20% 45%; /* Enhanced starting position */
    /* Scroll-controlled parallax with refined transitions */
    width: 100%;
    max-width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    transition: background-position 0.08s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother easing */
}

body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent body scrolling */
    flex-direction: column;
    height: 100vh;
    position: fixed; /* Prevent iOS bounce scrolling */
}

/* Refined professional gradient animations */
@keyframes subtleShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gentleFlow {
    0% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Dark mode gradient animations */
@keyframes subtleShiftDark {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gentleFlowDark {
    0% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Performance optimizations and accessibility */
@media (prefers-reduced-motion: reduce) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation: none !important;
        background-position: 0% 50% !important;
        background-attachment: scroll !important; /* Disable parallax for reduced motion */
        transition: none !important; /* Disable scroll-based background transitions */
    }
}

/* GPU acceleration for smooth animations */
html,
.App {
    will-change: background-position;
    transform: translateZ(0);
    backface-visibility: hidden;
}

@keyframes pulse {
    /* Kept for compatibility */
}

.scroll-child {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    align-content: center;
    scroll-snap-align: start;
    height: 100vh;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 60px 2% 0 2%; /* Reduced top padding for better centering */
    overflow-x: hidden;
    scroll-snap-stop: always; /* Force snap on each section */
    /* Android-specific section isolation */
    position: relative;
    z-index: 1;
    /* Prevent content bleeding between sections */
    contain: layout style;
}

.contact-with-footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    /* Android-compatible viewport height */
    height: 100vh;
    min-height: 100vh;
    /* Modern browsers with dynamic viewport support */
    min-height: 100dvh;
    padding: 0;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    /* Android-specific fixes for content bleeding */
    position: relative;
    z-index: 10;
    background: var(--app-background, transparent);
    /* Ensure section isolation on Android */
    isolation: isolate;
}

.contact-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.footer-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 30px;
    margin-top: auto;
    min-height: 120px;
    position: relative;
}

/* Enhanced Android contact card centering fixes */
@media (max-width: 768px) {
    .contact-with-footer {
        /* Enhanced Android viewport handling */
        display: flex !important;
        flex-direction: column !important;
        justify-content: space-between !important;
        align-items: center !important;
        /* Better Android viewport compatibility */
        min-height: -webkit-fill-available;
        min-height: calc(100vh - env(keyboard-inset-height, 0px));
        /* Ensure proper centering */
        position: relative;
        overflow: hidden;
    }

    .contact-content {
        /* Perfect Android centering */
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        flex: 1 !important;
        width: 100% !important;
        /* Ensure content stays centered */
        position: relative;
        padding: 0 15px;
        box-sizing: border-box;
        /* Center the content vertically */
        min-height: 0;
        flex-grow: 1;
    }
}

/* Android portrait mode specific centering */
@media (max-width: 768px) and (orientation: portrait) {
    .contact-content {
        /* Perfect portrait centering for Android */
        justify-content: center !important;
        align-items: center !important;
        padding: 20px 15px;
        /* Use transform for perfect centering */
        display: flex !important;
        flex-direction: column !important;
    }
}

/* Android small screens enhanced centering */
@media (max-width: 480px) {
    .contact-content {
        /* Enhanced small screen centering */
        padding: 15px 10px;
        justify-content: center !important;
        align-items: center !important;
        /* Ensure perfect centering on small Android screens */
        position: relative;
        top: 50%;
        transform: translateY(-50%);
    }
}

.white-space {
}

/* Loading spinner */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.content-wrapper {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    color: rgba(36,59,8,0);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    scroll-padding-top: 0; /* Handled by section padding */
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.content-wrapper::-webkit-scrollbar {
    display: none;
}

.title {
    color: #1c1c1e; /* iOS primary label color */
}

.subtitle {
    color: #3c3c43; /* iOS secondary label color */
}


.App {
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.45) 0%,
        rgba(249, 249, 249, 0.38) 8%,
        rgba(242, 242, 247, 0.32) 18%,
        rgba(229, 229, 234, 0.28) 32%,
        rgba(209, 209, 214, 0.35) 48%,
        rgba(199, 199, 204, 0.42) 65%,
        rgba(174, 174, 178, 0.48) 82%,
        rgba(142, 142, 147, 0.55) 100%
    );
    background-size: 220% 220%; /* Amplified size for enhanced parallax */
    background-attachment: fixed;
    background-position: 85% 30%; /* Refined starting position */
    /* Removed time-based animation - now scroll-controlled */
    height: 100vh;
    width: 100%;
    overflow: hidden; /* No scrolling on App level */
    padding: 0;
    margin: 0;
    position: relative;
    transition: background-position 0.12s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Refined easing */
}
/* Dark Mode (Applied via class) */

/* iOS Dark Mode Background - Native iOS Dark Theme */
.dark html {
    background: linear-gradient(
        180deg,
        #000000 0%,
        #1c1c1e 8%,
        #2c2c2e 18%,
        #3a3a3c 32%,
        #48484a 48%,
        #636366 65%,
        #8e8e93 82%,
        #aeaeb2 100%
    );
    background-size: 240% 240%; /* Amplified size for enhanced dark mode parallax */
    background-attachment: fixed;
    background-position: 15% 40%; /* Enhanced starting position for dark mode */
    /* Scroll-controlled parallax with refined dark mode transitions */
    transition: background-position 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .App {
    background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0.85) 0%,
        rgba(28, 28, 30, 0.78) 8%,
        rgba(44, 44, 46, 0.68) 18%,
        rgba(58, 58, 60, 0.58) 32%,
        rgba(72, 72, 74, 0.48) 48%,
        rgba(99, 99, 102, 0.58) 65%,
        rgba(142, 142, 147, 0.68) 82%,
        rgba(174, 174, 178, 0.78) 100%
    );
    background-size: 210% 210%; /* Amplified size for enhanced dark mode parallax */
    background-attachment: fixed;
    background-position: 75% 25%; /* Refined starting position for dark mode */
    /* Scroll-controlled parallax with enhanced dark mode transitions */
    transition: background-position 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dark .homepage-container {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
}

.dark .homepage-container:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 8px 16px rgba(54, 54, 54, 0.4),
        0 0 20px rgba(255, 255, 255, 0.1);
}

.dark .link-text {
    color: var(--dark-primary-text);
}

.dark .title {
    color: var(--dark-primary-text);
}

.dark .subtitle {
    color: var(--dark-secondary-text);
}

/* Enhanced dark mode text hierarchy */
.dark h1, .dark h2, .dark h3 {
    color: var(--dark-primary-text);
}

.dark h4, .dark h5, .dark h6 {
    color: var(--dark-secondary-text);
}

.dark p, .dark span {
    color: var(--dark-secondary-text);
}

.dark .muted {
    color: var(--dark-tertiary-text);
}

/* Comprehensive dark mode text and element styling */
.dark input,
.dark textarea,
.dark select {
    background: var(--dark-glass-base);
    border: 1px solid var(--dark-glass-border);
    color: var(--dark-primary-text);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    border-color: var(--dark-glass-border-hover);
    outline: 2px solid var(--focus-ring);
}

.dark button {
    background: var(--dark-glass-base);
    border: 1px solid var(--dark-glass-border);
    color: var(--dark-secondary-text);
}

.dark button:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    color: var(--dark-primary-text);
}

.dark a {
    color: var(--dark-secondary-text);
}

.dark a:hover {
    color: var(--dark-primary-text);
}

/* Dark mode accessibility enhancements */
.dark :focus-visible {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
}

/* Dark mode high contrast support */
@media (prefers-contrast: high) {
    .dark {
        --dark-primary-text: #ffffff;
        --dark-secondary-text: #e0e0e0;
        --dark-tertiary-text: #c0c0c0;
        --dark-glass-border: rgba(255, 255, 255, 0.5);
        --dark-glass-border-hover: rgba(255, 255, 255, 0.7);
    }
}

/* Toggle Switch Style - Legacy (now handled by navbar) */
.dark-mode-toggle {
    display: none; /* Hidden as we now use the navbar toggle */
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

/* Large screens and tablets */
@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
        scroll-snap-type: y mandatory;
        height: 100vh;
    }

    .scroll-child {
        width: 100%;
        max-width: 100vw;
        padding: 50px 3% 0 3%; /* Reduced for better centering on large screens */
        scroll-snap-align: start;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Tablets */
@media (max-width: 1024px) {
    .scroll-child {
        padding: 50px 4% 0 4%; /* Reduced for better centering on tablets */
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .scroll-child {
        padding: 45px 4% 0 4%; /* Reduced for better centering on mobile landscape */
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }

    .footer-wrapper {
        min-height: 100px;
        padding-bottom: 25px;
    }

    /* Android-specific contact layout adjustments */
    .contact-with-footer {
        /* Account for Android browser UI bars */
        min-height: calc(100vh - 60px);
        min-height: calc(100dvh - 60px);
        /* Fallback for older Android browsers */
        height: auto;
        min-height: -webkit-fill-available;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .scroll-child {
        padding: 40px 2.5% 0 2.5%; /* Reduced for better centering on mobile portrait */
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }

    .footer-wrapper {
        min-height: 90px;
        padding-bottom: 20px;
    }

    /* Enhanced Android compatibility for mobile portrait */
    .contact-with-footer {
        /* More space for Android browser UI in portrait */
        min-height: calc(100vh - 80px);
        min-height: calc(100dvh - 80px);
        /* Chrome on Android specific */
        min-height: -webkit-fill-available;
        /* Ensure footer remains visible */
        padding-bottom: 20px;
    }
}

/* Very small screens */
@media (max-width: 380px) {
    .scroll-child {
        padding: 35px 1% 0 1%; /* Reduced for better centering on very small screens */
        width: 100%;
    }

    .footer-wrapper {
        min-height: 80px;
        padding-bottom: 15px;
    }

    /* Critical Android fixes for very small screens */
    .contact-with-footer {
        /* Maximum space for Android browser UI on small screens */
        min-height: calc(100vh - 100px);
        min-height: calc(100dvh - 100px);
        /* Chrome on Android fallback */
        min-height: -webkit-fill-available;
        /* Ensure adequate bottom spacing */
        padding-bottom: 30px;
        /* Allow scrolling if content overflows */
        overflow-y: auto;
    }
}

/* Mobile scroll-snap optimizations */
@media (max-width: 768px) {
    .content-wrapper {
        scroll-snap-type: y mandatory;
        /* Improve touch scrolling on mobile */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
    }

    .scroll-child {
        scroll-snap-stop: always;
    }

    /* Optimize background animations for mobile */
    html,
    .App {
        animation-duration: 280s, 360s;
        background-size: 160% 160%;
        background-attachment: scroll; /* Disable parallax on mobile for performance */
    }

    .dark html,
    .dark .App {
        animation-duration: 300s, 380s;
        background-size: 160% 160%;
        background-attachment: scroll; /* Disable parallax on mobile for performance */
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        /* More aggressive snap on small screens */
        scroll-snap-type: y mandatory;
    }

    /* Further optimize for small screens */
    html,
    .App {
        animation-duration: 300s, 360s;
        background-size: 130% 130%;
        background-attachment: scroll; /* Disable parallax on small screens */
    }

    .dark html,
    .dark .App {
        animation-duration: 320s, 380s;
        background-size: 130% 130%;
        background-attachment: scroll; /* Disable parallax on small screens */
    }
}

/* Very small screens - minimal animation */
@media (max-width: 380px) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation-duration: 400s;
        background-size: 120% 120%;
        background-attachment: scroll; /* Disable parallax on very small screens */
    }
}

/* Android Browser Specific Fixes */
/* Chrome on Android specific viewport handling */
@supports (-webkit-appearance: none) {
    @media (max-width: 768px) {
        .content-wrapper {
            /* Chrome on Android viewport optimization */
            height: 100vh;
            min-height: -webkit-fill-available;
            /* Improve scrolling performance */
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        .contact-with-footer {
            /* Chrome on Android contact layout */
            min-height: calc(100vh - 80px);
            min-height: calc(-webkit-fill-available - 80px);
            /* Ensure footer visibility */
            padding-bottom: 40px;
        }

        .footer-wrapper {
            /* Chrome on Android footer positioning */
            margin-bottom: 20px;
            padding-bottom: 20px;
            /* Prevent footer cutoff */
            position: relative;
            z-index: 10;
        }
    }
}

/* Android Firefox specific fixes */
@-moz-document url-prefix() {
    @media (max-width: 768px) {
        .contact-with-footer {
            /* Firefox on Android layout */
            min-height: calc(100vh - 70px);
            padding-bottom: 35px;
        }

        .footer-wrapper {
            /* Firefox on Android footer */
            margin-bottom: 25px;
            padding-bottom: 15px;
        }
    }
}

/* Android orientation-specific fixes */
@media (max-width: 768px) and (orientation: portrait) {
    .contact-with-footer {
        /* Android portrait mode optimization */
        min-height: calc(100vh - 90px);
        min-height: calc(100dvh - 90px);
        /* Account for Android gesture navigation */
        padding-bottom: 40px;
        /* Ensure proper scrolling */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .footer-wrapper {
        /* Portrait mode footer spacing */
        margin-bottom: 30px;
        padding-bottom: 25px;
        /* Prevent gesture navigation interference */
        margin-top: auto;
    }
}

@media (max-width: 768px) and (orientation: landscape) {
    .contact-with-footer {
        /* Android landscape mode optimization */
        min-height: calc(100vh - 60px);
        min-height: calc(100dvh - 60px);
        /* Less bottom padding in landscape */
        padding-bottom: 20px;
    }

    .footer-wrapper {
        /* Landscape mode footer spacing */
        margin-bottom: 15px;
        padding-bottom: 10px;
    }
}

/* Android gesture navigation compatibility */
@media (max-width: 768px) {
    /* Detect Android devices with gesture navigation */
    @supports (padding: max(0px)) {
        .contact-with-footer {
            /* Account for gesture navigation area */
            padding-bottom: max(30px, env(safe-area-inset-bottom));
        }

        .footer-wrapper {
            /* Safe area for gesture navigation */
            margin-bottom: max(20px, env(safe-area-inset-bottom));
        }
    }
}

/* Android-Specific Section Bleeding Prevention & Enhanced Z-Index Stacking */
/* Critical fixes for Android browsers to prevent carousel content bleeding into contact section */

/* Chrome on Android - Enhanced section isolation and z-index stacking */
@supports (-webkit-appearance: none) {
    @media (max-width: 768px) {
        .content-wrapper {
            /* Enhanced scroll-snap for Chrome on Android */
            scroll-snap-type: y mandatory;
            scroll-behavior: smooth;
            /* Prevent content bleeding */
            overflow-y: auto;
            overflow-x: hidden;
            /* Improve section boundaries */
            scroll-padding-top: 0;
        }

        .scroll-child {
            /* Chrome on Android section isolation */
            scroll-snap-align: start;
            scroll-snap-stop: always;
            /* Prevent content overflow into next section */
            overflow: hidden;
            /* Ensure proper section boundaries */
            position: relative;
            z-index: 1;
            /* Create stacking context */
            isolation: isolate;
        }

        .contact-with-footer {
            /* Chrome on Android contact section priority */
            z-index: 20 !important;
            /* Ensure contact section covers carousel content */
            background: var(--app-background, inherit);
            /* Force section boundary */
            scroll-snap-align: start;
            scroll-snap-stop: always;
            /* Prevent carousel bleeding */
            position: relative;
            isolation: isolate;
            /* Strict containment */
            overflow: hidden;
        }

        /* Specific fix for projects section on Chrome Android */
        #projects.scroll-child {
            /* Contain carousel content within section */
            overflow: hidden !important;
            /* Prevent bleeding into contact section */
            contain: layout style paint !important;
            /* Ensure proper z-index stacking */
            z-index: 5 !important;
            /* Maximum height to prevent bleeding */
            max-height: 100vh;
            max-height: 100dvh;
        }
    }
}

/* Firefox on Android - Section isolation and z-index fixes */
@-moz-document url-prefix() {
    @media (max-width: 768px) {
        .content-wrapper {
            /* Firefox on Android scroll behavior */
            scroll-snap-type: y mandatory;
            scroll-behavior: smooth;
            /* Prevent section bleeding */
            overflow-y: auto;
            overflow-x: hidden;
        }

        .scroll-child {
            /* Firefox on Android section boundaries */
            scroll-snap-align: start;
            scroll-snap-stop: always;
            /* Prevent content overflow */
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .contact-with-footer {
            /* Firefox on Android contact section */
            z-index: 15 !important;
            background: var(--app-background, inherit);
            position: relative;
            isolation: isolate;
            overflow: hidden;
        }

        #projects.scroll-child {
            /* Firefox specific carousel containment */
            overflow: hidden !important;
            contain: layout style !important;
            z-index: 5 !important;
            max-height: 100vh;
        }
    }
}

/* Samsung Internet and other Android browsers - Portrait mode */
@media (max-width: 768px) and (orientation: portrait) {
    .content-wrapper {
        /* Android portrait scroll-snap optimization */
        scroll-snap-type: y mandatory;
        scroll-behavior: smooth;
        /* Enhanced section isolation */
        overflow-y: auto;
        overflow-x: hidden;
        /* Prevent content bleeding */
        scroll-padding-top: 0;
    }

    .scroll-child {
        /* Android portrait section boundaries */
        scroll-snap-align: start;
        scroll-snap-stop: always;
        /* Strict content containment */
        overflow: hidden;
        position: relative;
        z-index: 1;
        /* Prevent bleeding between sections */
        contain: layout style paint;
    }

    .contact-with-footer {
        /* Android portrait contact section priority */
        z-index: 25 !important;
        /* Ensure contact section is above carousel */
        background: var(--app-background, inherit);
        /* Force proper section boundary */
        position: relative;
        isolation: isolate;
        /* Prevent carousel content visibility */
        overflow: hidden;
    }

    /* Critical fix for projects section bleeding */
    #projects.scroll-child {
        /* Strict carousel containment in portrait */
        overflow: hidden !important;
        contain: layout style paint !important;
        z-index: 5 !important;
        /* Ensure carousel doesn't bleed into contact */
        max-height: 100vh;
        max-height: 100dvh;
    }
}

/* Android landscape mode - Section isolation fixes */
@media (max-width: 768px) and (orientation: landscape) {
    .scroll-child {
        /* Android landscape section containment */
        overflow: hidden;
        contain: layout style;
        position: relative;
        z-index: 1;
    }

    .contact-with-footer {
        /* Android landscape contact section */
        z-index: 15 !important;
        background: var(--app-background, inherit);
        position: relative;
        isolation: isolate;
        overflow: hidden;
    }

    #projects.scroll-child {
        /* Landscape carousel containment */
        overflow: hidden !important;
        contain: layout style !important;
        z-index: 5 !important;
        /* Adjust height for landscape */
        max-height: 100vh;
        max-height: 100dvh;
    }
}

/* Android very small screens - Critical section isolation */
@media (max-width: 380px) {
    .scroll-child {
        /* Very small Android screens section containment */
        overflow: hidden !important;
        contain: layout style paint !important;
        position: relative;
        z-index: 1;
        /* Maximum content containment */
        clip-path: inset(0);
    }

    .contact-with-footer {
        /* Very small screens contact section priority */
        z-index: 30 !important;
        background: var(--app-background, inherit);
        position: relative;
        isolation: isolate;
        overflow: hidden;
        /* Absolute prevention of carousel bleeding */
        clip-path: inset(0);
    }

    #projects.scroll-child {
        /* Very small screens carousel strict containment */
        overflow: hidden !important;
        contain: layout style paint !important;
        z-index: 5 !important;
        max-height: calc(100vh - 50px);
        max-height: calc(100dvh - 50px);
        /* Absolute content containment */
        clip-path: inset(0);
    }
}