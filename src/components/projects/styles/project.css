@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 280px; /* Compact height for content */
    height: 280px; /* Fixed height for consistency */
    width: 100%;
    max-width: 90%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px */
    margin: 0 auto;
    overflow: visible; /* Prevent content overflow */
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: visible;
    justify-content: center; /* Center content vertically */
    align-items: flex-start; /* Align content to left */
    text-align: left; /* Left text alignment */
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - minimal margin for maximum content space */
    flex-shrink: 0; /* Prevent header from shrinking */
    min-height: 40px; /* Reduced header height for content density */
}

.project-logo {
    width: 45px; /* Slightly reduced for better content balance */
    height: 45px;
    padding-right: var(--spacing-md); /* 1rem = 16px - reduced padding for content density */
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.project-title h3 {
    font-family: var(--secondary-font); /* Montserrat for headings */
    font-size: 1.6rem; /* Increased for better desktop readability */
    margin: 0;
    color: var(--primary-color);
    font-weight: var(--heading-weight); /* 600 */
    line-height: 1.2; /* Improved line height for readability */
    letter-spacing: -0.02em; /* Consistent with global h3 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Dark mode project styling */
.dark .project-title h3 {
    color: var(--dark-primary-text);
}

.dark .project-description {
    color: var(--dark-secondary-text);
}

.dark .project-content .subtitle {
    color: var(--dark-secondary-text);
}

.dark .project-description-list li {
    color: var(--dark-tertiary-text);
}

.dark .project-link {
    color: var(--dark-secondary-text);
}

.dark .project-link-text {
    color: var(--dark-secondary-text);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 var(--spacing-sm); /* 0.5rem = 8px */
    min-height: 0; /* Allow flex item to shrink */
    max-height: calc(100% - 60px); /* Ensure content doesn't overflow container */
    text-align: left; /* Left text alignment */
    align-items: flex-start; /* Align content to left */
}


.project-description {
    color: var(--secondary-color);
}

/* Subtitle styling for project description title */
.project-content .subtitle {
    font-family: var(--primary-font); /* Poppins for body text */
    color: var(--secondary-color);
    font-size: 1.1rem; /* Increased for better desktop readability */
    font-weight: 500;
    margin: 0 0 var(--spacing-sm) 0; /* 0.5rem = 8px - increased margin */
    line-height: 1.3; /* Improved line height for readability */
    letter-spacing: 0.01em; /* Subtle letter spacing */
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: left; /* Left alignment */
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    font-family: var(--primary-font); /* Poppins for body text */
    padding-left: var(--spacing-lg); /* 1.5rem = 24px */
    margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* 0.5rem = 8px - increased margins for better spacing */
    list-style-type: disc;
    overflow: visible; /* Remove scrolling */
    flex: 1;
    font-size: 1rem; /* Increased for better desktop readability */
    line-height: 1.5; /* Improved line height for better readability */
    height: auto; /* Allow natural height */
    text-align: left; /* Keep list items left-aligned for readability */
    max-width: 100%; /* Ensure list doesn't overflow */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-description-list::-webkit-scrollbar {
    display: none;
}

.project-description-list li {
    margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - increased for better readability */
    line-height: 1.5; /* Improved line height for better readability */
    position: relative;
    padding-left: var(--spacing-sm); /* 0.5rem = 8px - increased padding for better visual hierarchy */
    color: var(--secondary-color);
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    text-align: left; /* Keep list items left-aligned for readability */
}

/* Large Desktop Screens (1270px+) - Consistent with carousel heights */
@media (min-width: 1270px) {
    .project {
        min-height: 320px; /* Consistent with carousel slide height */
        height: 320px;
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px */
    }

    .project-header {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        min-height: 35px;
    }

    .project-logo {
        width: 42px;
        height: 42px;
        padding-right: var(--spacing-sm); /* 0.5rem = 8px */
    }

    .project-title h3 {
        font-size: 1.8rem; /* Enhanced for large screens */
        line-height: 1.1;
    }

    .project-content .subtitle {
        font-size: 1.2rem;
        margin-bottom: var(--spacing-xs);
        line-height: 1.2;
    }

    .project-description-list {
        font-size: 1.1rem;
        margin: var(--spacing-xs) 0;
        line-height: 1.4;
        max-height: calc(100% - 30px);
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs);
        line-height: 1.4;
    }
}

/* Desktop Screens (1024px-1269px) - Balanced layout */
@media (min-width: 1024px) and (max-width: 1269px) {
    .project {
        min-height: 340px; /* Consistent with carousel slide height */
        height: 340px;
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px */
    }

    .project-header {
        margin-bottom: var(--spacing-xs);
        min-height: 38px;
    }

    .project-logo {
        width: 40px;
        height: 40px;
        padding-right: var(--spacing-sm);
    }

    .project-title h3 {
        font-size: 1.7rem;
        line-height: 1.1;
    }

    .project-content .subtitle {
        font-size: 1.15rem;
        margin-bottom: var(--spacing-xs);
        line-height: 1.2;
    }

    .project-description-list {
        font-size: 1.05rem;
        margin: var(--spacing-xs) 0;
        line-height: 1.4;
        max-height: calc(100% - 28px);
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs);
        line-height: 1.4;
    }
}

/* Tablet Screens (769px-1023px) - Touch-optimized */
@media (min-width: 769px) and (max-width: 1023px) {
    .project {
        min-height: 380px; /* Consistent with carousel slide height */
        height: 380px;
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px */
    }

    .project-header {
        margin-bottom: var(--spacing-sm);
        min-height: 40px;
    }

    .project-logo {
        width: 38px;
        height: 38px;
        padding-right: var(--spacing-md);
    }

    .project-title h3 {
        font-size: 1.5rem;
        line-height: 1.2;
    }

    .project-content .subtitle {
        font-size: 1.05rem;
        margin-bottom: var(--spacing-sm);
        line-height: 1.3;
    }

    .project-description-list {
        font-size: 0.95rem;
        margin: var(--spacing-sm) 0;
        line-height: 1.4;
        padding-left: var(--spacing-lg);
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm);
        line-height: 1.4;
    }
}

/* Small Tablet/Large Mobile (481px-768px) - Consistent with carousel */
@media (min-width: 481px) and (max-width: 768px) {
    .project {
        min-height: 480px; /* Consistent with carousel slide height */
        height: 480px;
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px */
    }

    .project-header {
        margin-bottom: var(--spacing-sm);
        min-height: 42px;
    }

    .project-logo {
        width: 36px;
        height: 36px;
        padding-right: var(--spacing-md);
    }

    .project-title h3 {
        font-size: 1.2rem;
        line-height: 1.2;
    }

    .project-content .subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-sm);
        line-height: 1.3;
    }

    .project-description-list {
        font-size: 0.8rem;
        padding-left: var(--spacing-lg);
        line-height: 1.4;
        margin: var(--spacing-sm) 0;
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm);
        line-height: 1.4;
    }
}

/* Mobile Portrait (381px-480px) - Consistent with carousel */
@media (min-width: 381px) and (max-width: 480px) {
    .project {
        min-height: 360px !important; /* Increased for Android compatibility */
        height: 360px !important;
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-lg) var(--spacing-md) !important; /* Top Right Bottom Left - extra bottom padding */
        overflow: visible !important;
        box-sizing: border-box !important;
    }

    .project-header {
        margin-bottom: var(--spacing-sm) !important;
        min-height: 40px !important;
        flex-shrink: 0 !important;
    }

    .project-logo {
        width: 32px !important;
        height: 32px !important;
        padding-right: var(--spacing-sm) !important;
    }

    .project-title h3 {
        font-size: 1.1rem !important; /* Smaller for mobile */
        line-height: 1.2 !important;
        margin: 0 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .project-content {
        padding: 0 var(--spacing-sm) !important;
        flex: 1 !important;
        overflow: visible !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .project-content .subtitle {
        font-size: 0.9rem !important; /* Smaller for mobile */
        margin-bottom: var(--spacing-sm) !important;
        line-height: 1.3 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .project-description-list {
        font-size: 0.7rem !important; /* Much smaller for mobile */
        padding-left: var(--spacing-md) !important; /* Reduced padding */
        margin: var(--spacing-sm) 0 !important;
        line-height: 1.3 !important; /* Tighter line height */
        height: auto !important;
        overflow: visible !important;
        flex: 1 !important;
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm) !important;
        line-height: 1.4 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }
}

/* Very Small Mobile (320px-380px) - Consistent with carousel */
@media (max-width: 380px) {
    .project {
        min-height: 380px !important; /* Increased for Android compatibility */
        height: 380px !important;
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xl) var(--spacing-md) !important; /* Top Right Bottom Left - extra bottom padding */
        overflow: visible !important;
        box-sizing: border-box !important;
    }

    .project-header {
        margin-bottom: var(--spacing-sm) !important;
        min-height: 38px !important;
        flex-shrink: 0 !important;
    }

    .project-logo {
        width: 30px !important;
        height: 30px !important;
        padding-right: var(--spacing-sm) !important;
    }

    .project-title h3 {
        font-size: 1.25rem !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .project-content {
        padding: 0 var(--spacing-xs) !important;
        flex: 1 !important;
        overflow: visible !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .project-content .subtitle {
        font-size: 0.85rem !important; /* Smaller for very small mobile */
        margin-bottom: var(--spacing-sm) !important;
        line-height: 1.3 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .project-description-list {
        font-size: 0.65rem !important; /* Very small for tiny screens */
        padding-left: var(--spacing-sm) !important; /* Minimal padding */
        margin: var(--spacing-xs) 0 !important;
        line-height: 1.3 !important; /* Tight line height */
        height: auto !important;
        overflow: visible !important;
        flex: 1 !important;
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm) !important;
        line-height: 1.4 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }
}

/* Universal Android project card centering enhancements */
@media (max-width: 768px) {
    .project {
        /* Enhanced Android project card centering */
        margin: 0 auto !important;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        /* Perfect card centering */
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
    }

    .project-container {
        /* Enhanced Android project container centering */
        width: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
    }

    .project-header {
        /* Perfect header centering */
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }

    .project-title {
        /* Perfect title centering */
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }

    .project-title h3 {
        /* Title text centering */
        text-align: center !important;
        width: 100% !important;
        margin: 0 auto !important;
    }

    .project-content {
        /* Perfect content centering */
        width: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
    }

    .project-content .subtitle {
        /* Subtitle centering */
        text-align: center !important;
        width: 100% !important;
        margin: 0 auto !important;
    }

    .project-description-list {
        /* List centering */
        width: 100% !important;
        text-align: center !important;
        margin: 0 auto !important;
        padding: 0 !important;
        list-style-position: inside !important;
    }

    .project-description-list li {
        /* List item centering */
        text-align: center !important;
        width: 100% !important;
        margin: 0 auto !important;
    }
}










