import React, {useEffect, useState} from 'react';
import {Link, scrollSpy} from 'react-scroll';
import './styles/navBar.css';
import {Divider} from '@mui/material';
import DarkModeSwitch from './darkModeSwitch.jsx';


const NavBar = () => {
	const [activeSection, setActiveSection] = useState('home');

	useEffect(() => {
		scrollSpy.update();

		// Enhanced scroll detection for active section
		const handleScroll = () => {
			const contentWrapper = document.getElementById('content-wrapper');
			if (!contentWrapper) return;

			const sections = ['home', 'projects', 'contact'];
			const scrollTop = contentWrapper.scrollTop;
			const windowHeight = contentWrapper.clientHeight;

			// Find which section is most visible
			let currentSection = 'home';
			let maxVisibility = 0;

			sections.forEach(sectionId => {
				const element = document.getElementById(sectionId);
				if (element) {
					const rect = element.getBoundingClientRect();
					const containerRect = contentWrapper.getBoundingClientRect();

					// Calculate relative position within the scroll container
					const elementTop = rect.top - containerRect.top;
					const elementBottom = rect.bottom - containerRect.top;

					// Calculate how much of the section is visible
					const visibleTop = Math.max(0, -elementTop);
					const visibleBottom = Math.min(windowHeight, windowHeight - Math.max(0, elementBottom - windowHeight));
					const visibleHeight = Math.max(0, visibleBottom - visibleTop);
					const totalHeight = rect.height;
					const visibility = visibleHeight / totalHeight;

					// Consider a section active if it's more than 30% visible
					// or if we're in the top 20% of the viewport (for home section)
					const isInTopArea = elementTop <= windowHeight * 0.2 && elementTop >= -windowHeight * 0.3;

					if ((visibility > maxVisibility && visibility > 0.3) ||
						(sectionId === 'home' && isInTopArea) ||
						(visibility > 0.1 && elementTop <= 100 && elementTop >= -100)) {
						maxVisibility = visibility;
						currentSection = sectionId;
					}
				}
			});

			setActiveSection(currentSection);
		};

		const contentWrapper = document.getElementById('content-wrapper');
		if (contentWrapper) {
			contentWrapper.addEventListener('scroll', handleScroll);
			// Initial check
			handleScroll();
		}

		return () => {
			if (contentWrapper) {
				contentWrapper.removeEventListener('scroll', handleScroll);
			}
		};
	}, []);


	return (
		<React.Fragment>
			<div className="nav-container navbar-slide-down">
				<nav className="navbar">
					<div className="nav-background bg-blur glass-hover">
						<ul className="nav-list">
							<li className={`nav-item smooth-transition ${activeSection === 'home' ? 'active' : ''}`}>
								<Link
									spy={true}
									to="home"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Home</span>
								</Link>
							</li>
							<li className={`nav-item smooth-transition ${activeSection === 'projects' ? 'active' : ''}`}>
								<Link
									spy={true}
									to="projects"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Skills</span>
								</Link>
							</li>
							<li className={`nav-item smooth-transition ${activeSection === 'contact' ? 'active' : ''}`}>
								<Link
									spy={true}
									to="contact"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Contact</span>
								</Link>
							</li>
						</ul>
						<Divider orientation="vertical" variant='middle' className='divider' flexItem/>
						<DarkModeSwitch/>
					</div>
				</nav>
			</div>
		</React.Fragment>
	);
};


export default NavBar;
